"use client";

import { SidebarTab } from "@/stores/sidebarStore";
import { useEffect, useRef, useState } from "react";

interface SidebarTabsProps {
  activeTab: SidebarTab;
  onTabChange: (tab: SidebarTab) => void;
}

export function SidebarTabs({ activeTab, onTabChange }: SidebarTabsProps) {
  const blocksRef = useRef<HTMLButtonElement>(null);
  const toolsRef = useRef<HTMLButtonElement>(null);
  const [underlineStyle, setUnderlineStyle] = useState({
    width: 0,
    transform: "",
  });

  useEffect(() => {
    const activeRef = activeTab === "Blocks" ? blocksRef : toolsRef;
    if (activeRef.current) {
      const rect = activeRef.current.getBoundingClientRect();
      const parentRect = activeRef.current.parentElement?.getBoundingClientRect();
      const offsetLeft = parentRect ? rect.left - parentRect.left : 0;

      setUnderlineStyle({
        width: rect.width,
        transform: `translateX(${offsetLeft}px)`,
      });
    }
  }, [activeTab]);

  return (
    <div className="relative pt-1 pb-2">
      <div className="flex gap-8 px-4">
        <button
          ref={blocksRef}
          onClick={() => onTabChange("Blocks")}
          className={`font-medium text-sm transition-colors hover:text-foreground ${
            activeTab === "Blocks" ? "text-foreground" : "text-muted-foreground"
          }`}
        >
          Blocks
        </button>
        <button
          ref={toolsRef}
          onClick={() => onTabChange("Tools")}
          className={`font-medium text-sm transition-colors hover:text-foreground ${
            activeTab === "Tools" ? "text-foreground" : "text-muted-foreground"
          }`}
        >
          Tools
        </button>
      </div>

      <div className="relative mt-2">
        <div className="absolute bottom-0 h-[1px] w-full border-b border-border" />
        <div
          className="absolute bottom-0 h-[1.5px] bg-primary-500 transition-transform duration-200"
          style={{
            width: `${underlineStyle.width}px`,
            transform: underlineStyle.transform,
          }}
        />
      </div>
    </div>
  );
}
